import { InputType, Field } from '@nestjs/graphql';
import {
  IsOptional,
  IsString,
  IsArray,
  IsBoolean,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { ClubStatus } from '../entities/club.entity';

@InputType()
export class ClubsInput {
  /** Filter by club slug */
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  slug?: string;

  /** Filter by club status */
  @Field(() => ClubStatus, { nullable: true })
  @IsOptional()
  status?: ClubStatus;

  /** Filter by city ID */
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  city?: string;

  /** Filter by neighborhood ID */
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  neighborhood?: string;

  /** Filter by category IDs */
  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  categories?: string[];

  /** Filter by featured status */
  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  /** Filter by minimum rating */
  @Field({ nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  minRating?: number;

  /** Filter by maximum rating */
  @Field({ nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  maxRating?: number;
}
