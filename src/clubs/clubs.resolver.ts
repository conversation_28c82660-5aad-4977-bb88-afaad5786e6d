import {
  Args,
  Context,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import * as _ from 'lodash';
import { FilterQuery } from 'mongoose';
import { GqlContext } from 'src/app.module';
import { PaginationInput } from 'src/common/pagination.input';
import { ClubsService } from './clubs.service';
import { ClubsInput } from './dto/club.input';
import { CreateClubInput } from './dto/create-club.input';
import { UpdateClubInput } from './dto/update-club.input';
import { Club, PaginatedClubs } from './entities/club.entity';
import { Public } from 'src/auth/guards/auth.guard';
import { ClubCategory } from './entities/club-categories.entity';

@Resolver(() => Club)
export class ClubsResolver {
  constructor(private readonly clubsService: ClubsService) {}

  @ResolveField(() => Club, { name: 'city' })
  getLocation(@Parent() club: Club, @Context() context: GqlContext) {
    return context.loaders.citiesLoader.load(club.city);
  }

  @ResolveField(() => Club, { name: 'neighborhood', nullable: true })
  getNeighborhood(@Parent() club: Club, @Context() context: GqlContext) {
    if (!club.neighborhood) return null;
    return context.loaders.neighborhoodsLoader.load(club.neighborhood);
  }

  @ResolveField(() => [ClubCategory], { name: 'categories' })
  getCategories(@Parent() club: Club, @Context() context: GqlContext) {
    return context.loaders.clubCategoriesLoader.loadMany(club.categories);
  }

  @Mutation(() => Club)
  createClub(@Args('createClubInput') createClubInput: CreateClubInput) {
    return this.clubsService.create(createClubInput);
  }

  @Public()
  @Query(() => PaginatedClubs, {
    name: 'clubs',
    description: 'Get all clubs with pagination and filtering',
  })
  findAllClubs(
    @Args('clubsInput', { nullable: true })
    clubsInput?: ClubsInput,
    @Args('paginationInput', { type: () => PaginationInput, nullable: true })
    paginationInput?: PaginationInput,
  ) {
    if (!clubsInput) clubsInput = {};
    else clubsInput = _.omitBy(clubsInput, (value) => _.isUndefined(value));

    const filter: FilterQuery<Club> = {};

    // Add exact match for slug if provided
    if (clubsInput.slug) filter.slug = clubsInput.slug;

    // Handle category filtering (match any of the provided categories)
    if (
      clubsInput.categories &&
      Array.isArray(clubsInput.categories) &&
      clubsInput.categories.length > 0
    ) {
      filter.categories = { $in: clubsInput.categories };
    }

    // Handle rating range filtering
    if (
      clubsInput.minRating !== undefined ||
      clubsInput.maxRating !== undefined
    ) {
      const ratingFilter: { [key: string]: number } = {};
      if (clubsInput.minRating !== undefined) {
        ratingFilter.$gte = clubsInput.minRating;
      }
      if (clubsInput.maxRating !== undefined) {
        ratingFilter.$lte = clubsInput.maxRating;
      }
      filter.rating = ratingFilter;
    }

    return this.clubsService.findAll(filter, paginationInput);
  }

  @Query(() => Club, { name: 'club' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.clubsService.findOne(id);
  }

  @Mutation(() => Club)
  updateClub(@Args('updateClubInput') updateClubInput: UpdateClubInput) {
    return this.clubsService.update(updateClubInput.id, updateClubInput);
  }

  @Mutation(() => Club)
  removeClub(@Args('id', { type: () => String }) id: string) {
    return this.clubsService.remove(id);
  }
}
