import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AppLayoutService } from './app-layout.service';
import { AppLayoutResolver } from './app-layout.resolver';
import { AppLayout, AppLayoutSchema } from './entities/app-layout.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AppLayout.name, schema: AppLayoutSchema },
    ]),
  ],
  providers: [AppLayoutResolver, AppLayoutService],
  exports: [AppLayoutService],
})
export class AppLayoutModule {}
