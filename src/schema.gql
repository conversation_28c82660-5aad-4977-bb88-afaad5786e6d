# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Location {
  """Center coordinates as [longitude, latitude]"""
  center: [Float!]!
}

type Address {
  """Address"""
  address: String!

  """Location"""
  location: Location!

  """Metro line"""
  metroLine: String

  """Metro station"""
  metroStation: String
}

type Contact {
  """Country code (e.g., +1, +91)"""
  countryCode: String!

  """Phone number"""
  phone: String!
}

type User {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """User fullname"""
  fullname: String!

  """User's email"""
  email: String!

  """User active status"""
  userStatus: UserStatus!

  """User role"""
  role: UserRoles!
  password: String!

  """User contact information"""
  contact: Contact!

  """Whether user has accepted terms and conditions"""
  acceptedTermsAndConditions: Boolean!

  """Whether user wants to receive discounts, royalty offers and updates"""
  subscribeToUpdates: Boolean!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

enum UserStatus {
  ACTIVE
  INACTIVE
}

enum UserRoles {
  ADMIN
  USER
}

type PaginatedUsers {
  """Array of documents"""
  docs: [User!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type AuthOutput {
  """Access token"""
  access_token: String!
}

type ChangePasswordOutput {
  """Success message"""
  message: String!
}

type PresignedFields {
  key: String!
  bucket: String!
  acl: String!
  algorithm: String!
  credential: String!
  date: String!
  Policy: String!
  signature: String!
}

type SignedUploadUrl {
  url: String!
  fields: PresignedFields!
}

type ClubCategory {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """Club Category name"""
  name: String!

  """Club Category description"""
  description: String

  """Club Category image"""
  image: String

  """Club Category Icon (support lucide-react)"""
  icon: String
}

type PaginatedClubCategories {
  """Array of documents"""
  docs: [ClubCategory!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type City {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """Name of the city"""
  name: String!

  """Main image of the city"""
  image: String!

  """Cover image for the city"""
  coverImage: String

  """Main heading for the city"""
  heading: String!

  """Sub heading for the city"""
  subHeading: String

  """Geographical location of the city"""
  location: Location!
}

type PaginatedCities {
  """Array of documents"""
  docs: [City!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type Neighborhood {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """SEO-friendly URL slug for the neighborhood"""
  slug: String!

  """Reference to the city this neighborhood belongs to"""
  city: City!

  """Name of the neighborhood"""
  name: String!

  """Geographical location of the neighborhood"""
  location: Location!

  """Main cover image of the neighborhood"""
  coverImage: String

  """Array of neighborhood image URLs"""
  images: [String!]
}

type PaginatedNeighborhoods {
  """Array of documents"""
  docs: [Neighborhood!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type DayTiming {
  timings: [Float!]!

  """Day of the week"""
  day: DayOfWeek!
}

enum DayOfWeek {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY
}

type BusinessHoursSchedule {
  """
  Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format
  """
  schedule: [DayTiming!]!
}

type MenuItem {
  """Menu item name"""
  name: String!

  """Menu item price"""
  price: Float!

  """Whether the item is available"""
  available: MenuItemAvailability!
}

enum MenuItemAvailability {
  AVAILABLE
  UNAVAILABLE
}

type MenuSection {
  """Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats")"""
  name: String!

  """Array of menu items in this section"""
  items: [MenuItem!]!
}

type Menu {
  """Menu currency (e.g., USD, EUR, INR)"""
  currency: String!

  """Array of menu sections"""
  sections: [MenuSection!]!
}

type Club {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """SEO-friendly URL slug for the club"""
  slug: String!
  categories: [ClubCategory!]!
  city: Club!
  neighborhood: Club

  """Club name"""
  name: String!

  """Club description"""
  description: String!

  """Club status"""
  status: ClubStatus!

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]!

  """Club phone number"""
  contact: Contact

  """Club address"""
  address: Address

  """Opening hours"""
  businessHours: BusinessHoursSchedule

  """Club menu"""
  menu: Menu

  """Whether club is featured"""
  featured: Boolean!

  """Club rating (0-5)"""
  rating: Float!
}

enum ClubStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
}

type PaginatedClubs {
  """Array of documents"""
  docs: [Club!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type BarCategory {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """Bar Category name"""
  name: String!

  """Bar Category description"""
  description: String

  """Bar Category image"""
  image: String

  """Bar Category Icon (support lucide-react)"""
  icon: String
}

type PaginatedBarCategories {
  """Array of documents"""
  docs: [BarCategory!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type BarDayTiming {
  """Day of the week"""
  day: DayOfWeek!
  timings: [Float!]!
}

type BarBusinessHoursSchedule {
  """
  Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format
  """
  schedule: [BarDayTiming!]!
}

type BarHappyHoursSchedule {
  """
  Array of happy hour day timings. Each day can have start time only or both start and end times in 24hr HHMM format
  """
  schedule: [BarDayTiming!]!
}

type BarMenuItem {
  """Menu item name"""
  name: String!

  """Menu item price"""
  price: Float!

  """Whether the item is available"""
  available: BarMenuItemAvailability!
}

enum BarMenuItemAvailability {
  AVAILABLE
  UNAVAILABLE
}

type BarMenuSection {
  """Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats")"""
  name: String!

  """Array of menu items in this section"""
  items: [BarMenuItem!]!
}

type BarMenu {
  """Menu currency (e.g., USD, EUR, INR)"""
  currency: String!

  """Array of menu sections"""
  sections: [BarMenuSection!]!
}

type Bar {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """Bar name"""
  name: String!

  """SEO-friendly URL slug for the bar"""
  slug: String!

  """Bar description"""
  description: String!
  categories: [BarCategory!]!
  city: City!
  neighborhood: Neighborhood

  """Bar status"""
  status: BarStatus!

  """Bar logo URL"""
  logo: String

  """Bar cover image URL"""
  coverImage: String

  """Array of bar image URLs"""
  images: [String!]!

  """Bar phone number"""
  contact: Contact

  """Bar address"""
  address: Address

  """Opening hours"""
  businessHours: BarBusinessHoursSchedule

  """Happy hours schedule"""
  happyHours: BarHappyHoursSchedule

  """Bar menu"""
  menu: BarMenu

  """Whether bar is featured"""
  featured: Boolean!

  """Bar rating (0-5)"""
  rating: Float!

  """Geographical location of the bar"""
  location: Location
}

enum BarStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
}

type PaginatedBars {
  """Array of documents"""
  docs: [Bar!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type Query {
  """Get all clubs with pagination and filtering"""
  clubs(clubsInput: ClubsInput, paginationInput: PaginationInput): PaginatedClubs!
  club(id: String!): Club!

  """Get all club categories with pagination and filtering"""
  clubCategories(clubCategoriesInput: ClubCategoriesInput, paginationInput: PaginationInput): PaginatedClubCategories!

  """Get a single club category by ID"""
  clubCategory(id: ID!): ClubCategory!

  """Get all bars with pagination and filtering"""
  bars(barsInput: BarsInput, paginationInput: PaginationInput): PaginatedBars!

  """Get a single bar by ID"""
  bar(id: ID!): Bar!

  """Get all bar categories with pagination and filtering"""
  barCategories(barCategoriesInput: BarCategoriesInput, paginationInput: PaginationInput): PaginatedBarCategories!

  """Get a single bar category by ID"""
  barCategory(id: ID!): BarCategory!

  """Get all cities with pagination and filtering"""
  cities(citiesInput: CitiesInput, paginationInput: PaginationInput): PaginatedCities!

  """Get a single city by ID"""
  city(id: ID!): City!
  users(paginationInput: PaginationInput): PaginatedUsers!
  user(id: String!): User!

  """Logged in  user"""
  me: User!

  """Get all neighborhoods with pagination and filtering"""
  neighborhoods(neighborhoodsInput: NeighborhoodsInput, paginationInput: PaginationInput): PaginatedNeighborhoods!

  """Get a single neighborhood by ID"""
  neighborhood(id: ID!): Neighborhood!
}

input ClubsInput {
  """Filter by club slug"""
  slug: String

  """Filter by club status"""
  status: ClubStatus

  """Filter by city ID"""
  city: String

  """Filter by neighborhood ID"""
  neighborhood: String

  """Filter by category IDs"""
  categories: [String!]

  """Filter by featured status"""
  featured: Boolean

  """Filter by minimum rating"""
  minRating: Float

  """Filter by maximum rating"""
  maxRating: Float
}

input PaginationInput {
  """Page number (1-based)"""
  page: Float! = 1

  """Number of items per page"""
  limit: Float! = 100

  """Sort configuration array"""
  sortConfig: [SortConfigInput!]! = [{field: "createdAt", order: DESC}]
}

input SortConfigInput {
  """Field to sort by"""
  field: String!

  """
  Sort order: "asc" or "desc"
  """
  order: SortOrder! = DESC
}

enum SortOrder {
  ASC
  DESC
}

input ClubCategoriesInput {
  """Filter by name (case-insensitive partial match)"""
  name: String

  """Pagination options"""
  pagination: PaginationInput
}

input BarsInput {
  """Filter by bar slug"""
  slug: String

  """Filter by bar status"""
  status: BarStatus

  """Filter by city ID"""
  city: String

  """Filter by neighborhood ID"""
  neighborhood: String

  """Filter by category IDs"""
  categories: [String!]

  """Filter by featured status"""
  featured: Boolean

  """Filter by minimum rating"""
  minRating: Float

  """Filter by maximum rating"""
  maxRating: Float

  """Filter by location proximity - sort by nearest to given coordinates"""
  nearLocation: LocationQueryInput

  """Filter by radius - find bars within specified radius"""
  withinRadius: RadiusQueryInput

  """Filter by bounding box - find bars within specified area"""
  withinBounds: BoundingBoxInput
}

input LocationQueryInput {
  """Longitude coordinate"""
  longitude: Float!

  """Latitude coordinate"""
  latitude: Float!

  """Maximum distance in meters (default: 10000m = 10km)"""
  maxDistance: Float = 10000
}

input RadiusQueryInput {
  """Center longitude coordinate"""
  longitude: Float!

  """Center latitude coordinate"""
  latitude: Float!

  """Radius in meters"""
  radiusMeters: Float!
}

input BoundingBoxInput {
  """Southwest longitude"""
  southWestLng: Float!

  """Southwest latitude"""
  southWestLat: Float!

  """Northeast longitude"""
  northEastLng: Float!

  """Northeast latitude"""
  northEastLat: Float!
}

input BarCategoriesInput {
  """Filter by name (case-insensitive partial match)"""
  name: String

  """Pagination options"""
  pagination: PaginationInput
}

input CitiesInput {
  """Filter by city name"""
  name: String
}

input NeighborhoodsInput {
  """Filter by neighborhood name"""
  name: String

  """Filter by city ID"""
  city: String

  """Pagination options"""
  pagination: PaginationInput
}

type Mutation {
  createClub(createClubInput: CreateClubInput!): Club!
  updateClub(updateClubInput: UpdateClubInput!): Club!
  removeClub(id: String!): Club!

  """Create a new club category"""
  createClubCategory(createClubCategoryInput: CreateClubCategoryInput!): ClubCategory!

  """Update an existing club category"""
  updateClubCategory(updateClubCategoryInput: UpdateClubCategoryInput!): ClubCategory!

  """Delete a club category"""
  deleteClubCategory(id: ID!): ClubCategory!

  """Create a new bar"""
  createBar(createBarInput: CreateBarInput!): Bar!

  """Update an existing bar"""
  updateBar(updateBarInput: UpdateBarInput!): Bar!

  """Delete a bar"""
  deleteBar(id: ID!): Bar!

  """Create a new bar category"""
  createBarCategory(createBarCategoryInput: CreateBarCategoryInput!): BarCategory!

  """Update an existing bar category"""
  updateBarCategory(updateBarCategoryInput: UpdateBarCategoryInput!): BarCategory!

  """Delete a bar category"""
  deleteBarCategory(id: ID!): BarCategory!

  """Create a new city"""
  createCity(createCityInput: CreateCityInput!): City!

  """Update an existing city"""
  updateCity(updateCityInput: UpdateCityInput!): City!

  """Delete a city"""
  deleteCity(id: ID!): City!
  createSignedUploadUrl(input: SignedUploadUrlInput!): SignedUploadUrl!
  signIn(input: SignInInput!): AuthOutput!
  signUp(input: CreateUserInput!): AuthOutput!
  changePassword(input: ChangePasswordInput!): ChangePasswordOutput!

  """Create a new neighborhood"""
  createNeighborhood(createNeighborhoodInput: CreateNeighborhoodInput!): Neighborhood!

  """Update an existing neighborhood"""
  updateNeighborhood(updateNeighborhoodInput: UpdateNeighborhoodInput!): Neighborhood!

  """Delete a neighborhood"""
  deleteNeighborhood(id: ID!): Neighborhood!
}

input CreateClubInput {
  """SEO-friendly URL slug for the club"""
  slug: String!

  """Club name"""
  name: String!

  """Club description"""
  description: String!

  """Club city IDs"""
  city: String!

  """Optional neighborhood ID"""
  neighborhood: String

  """Geographical location of the club"""
  location: LocationInput!

  """Club category IDs"""
  categories: [String!]!

  """Club status"""
  status: ClubStatus!

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]!

  """Club contact information (phone)"""
  contact: ContactInput

  """Club address"""
  address: AddressInput

  """Opening hours"""
  businessHours: BusinessHoursInput

  """Club menu"""
  menu: MenuInput

  """Whether club is featured"""
  featured: Boolean!

  """Club rating (0-5)"""
  rating: Float!
}

"""Inputs"""
input LocationInput {
  """Center coordinates as [longitude, latitude]"""
  center: [Float!]!
}

input ContactInput {
  """Country code (e.g., +1, +91)"""
  countryCode: String!

  """Phone number"""
  phone: String!
}

input AddressInput {
  """Address"""
  address: String!

  """Location"""
  location: LocationInput!

  """Metro line"""
  metroLine: String

  """Metro station"""
  metroStation: String
}

input BusinessHoursInput {
  """
  Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format
  """
  schedule: [DayTimingInput!]!
}

input DayTimingInput {
  """
  Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30). Supports formats that can be converted by time.utils: 900, 1730, etc.
  """
  timings: [Float!]!

  """Day of the week"""
  day: DayOfWeek!
}

input MenuInput {
  """Menu currency (e.g., USD, EUR, INR)"""
  currency: String!

  """Array of menu sections"""
  sections: [MenuSectionInput!]!
}

input MenuSectionInput {
  """Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats")"""
  name: String!

  """Array of menu items in this section"""
  items: [MenuItemInput!]!
}

input MenuItemInput {
  """Menu item name"""
  name: String!

  """Menu item price"""
  price: Float!

  """Whether the item is available"""
  available: MenuItemAvailability!
}

input UpdateClubInput {
  """SEO-friendly URL slug for the club"""
  slug: String

  """Club name"""
  name: String

  """Club description"""
  description: String

  """Club city IDs"""
  city: String

  """Optional neighborhood ID"""
  neighborhood: String

  """Geographical location of the club"""
  location: LocationInput

  """Club category IDs"""
  categories: [String!]

  """Club status"""
  status: ClubStatus

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]

  """Club contact information (phone)"""
  contact: ContactInput

  """Club address"""
  address: AddressInput

  """Opening hours"""
  businessHours: BusinessHoursInput

  """Club menu"""
  menu: MenuInput

  """Whether club is featured"""
  featured: Boolean

  """Club rating (0-5)"""
  rating: Float
  id: String!
}

input CreateClubCategoryInput {
  """Club Category name"""
  name: String!

  """Club Category description"""
  description: String

  """Club Category image"""
  image: String

  """Club Category Icon (support lucide-react)"""
  icon: String
}

input UpdateClubCategoryInput {
  """Club Category name"""
  name: String

  """Club Category description"""
  description: String

  """Club Category image"""
  image: String

  """Club Category Icon (support lucide-react)"""
  icon: String

  """Club Category ID"""
  id: ID!
}

input CreateBarInput {
  """Bar name"""
  name: String!

  """SEO-friendly URL slug for the bar"""
  slug: String!

  """Bar description"""
  description: String!

  """Bar city IDs"""
  city: String!

  """Optional neighborhood ID"""
  neighborhood: String

  """Geographical location of the bar"""
  location: LocationInput!

  """Bar category IDs"""
  categories: [String!]!

  """Bar status"""
  status: BarStatus!

  """Bar logo URL"""
  logo: String

  """Bar cover image URL"""
  coverImage: String

  """Array of bar image URLs"""
  images: [String!]!

  """Bar contact information (phone)"""
  contact: ContactInput

  """Bar address"""
  address: AddressInput

  """Opening hours"""
  businessHours: BarBusinessHoursInput

  """Happy hours schedule"""
  happyHours: BarHappyHoursInput

  """Bar menu"""
  menu: BarMenuInput

  """Whether bar is featured"""
  featured: Boolean!

  """Bar rating (0-5)"""
  rating: Float!
}

input BarBusinessHoursInput {
  """
  Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format
  """
  schedule: [DayTimingInput!]!
}

input BarHappyHoursInput {
  """
  Array of happy hour day timings. Each day can have start time only or both start and end times in 24hr HHMM format
  """
  schedule: [DayTimingInput!]!
}

input BarMenuInput {
  """Menu currency (e.g., USD, EUR, INR)"""
  currency: String!

  """Array of menu sections"""
  sections: [BarMenuSectionInput!]!
}

input BarMenuSectionInput {
  """Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats")"""
  name: String!

  """Array of menu items in this section"""
  items: [BarMenuItemInput!]!
}

input BarMenuItemInput {
  """Menu item name"""
  name: String!

  """Menu item price"""
  price: Float!

  """Whether the item is available"""
  available: BarMenuItemAvailability!
}

input UpdateBarInput {
  """Bar name"""
  name: String

  """SEO-friendly URL slug for the bar"""
  slug: String

  """Bar description"""
  description: String

  """Bar city IDs"""
  city: String

  """Optional neighborhood ID"""
  neighborhood: String

  """Geographical location of the bar"""
  location: LocationInput

  """Bar category IDs"""
  categories: [String!]

  """Bar status"""
  status: BarStatus

  """Bar logo URL"""
  logo: String

  """Bar cover image URL"""
  coverImage: String

  """Array of bar image URLs"""
  images: [String!]

  """Bar contact information (phone)"""
  contact: ContactInput

  """Bar address"""
  address: AddressInput

  """Opening hours"""
  businessHours: BarBusinessHoursInput

  """Happy hours schedule"""
  happyHours: BarHappyHoursInput

  """Bar menu"""
  menu: BarMenuInput

  """Whether bar is featured"""
  featured: Boolean

  """Bar rating (0-5)"""
  rating: Float
  id: String!
}

input CreateBarCategoryInput {
  """Bar Category name"""
  name: String!

  """Bar Category description"""
  description: String

  """Bar Category image"""
  image: String

  """Bar Category Icon (support lucide-react)"""
  icon: String
}

input UpdateBarCategoryInput {
  """Bar Category name"""
  name: String

  """Bar Category description"""
  description: String

  """Bar Category image"""
  image: String

  """Bar Category Icon (support lucide-react)"""
  icon: String

  """Bar Category ID"""
  id: ID!
}

input CreateCityInput {
  """Name of the city"""
  name: String!

  """Main image of the city"""
  image: String!

  """Cover image for the city"""
  coverImage: String

  """Main heading for the city"""
  heading: String!

  """Sub heading for the city"""
  subHeading: String

  """Geographical location of the city"""
  location: LocationInput!
}

input UpdateCityInput {
  """Name of the city"""
  name: String

  """Main image of the city"""
  image: String

  """Cover image for the city"""
  coverImage: String

  """Main heading for the city"""
  heading: String

  """Sub heading for the city"""
  subHeading: String

  """Geographical location of the city"""
  location: LocationInput

  """City ID"""
  id: ID!
}

input SignedUploadUrlInput {
  key: String!
  contentType: String!
  expiresIn: Float
}

input SignInInput {
  """User phone number"""
  email: String!

  """User password"""
  password: String!
}

input CreateUserInput {
  """User fullname"""
  fullname: String!

  """User phone number"""
  email: String!

  """User role"""
  role: UserRoles!

  """User password"""
  password: String!

  """User active status"""
  userStatus: UserStatus!

  """User contact information"""
  contact: ContactInput!

  """Whether user has accepted terms and conditions"""
  acceptedTermsAndConditions: Boolean!

  """Whether user wants to receive discounts, royalty offers and updates"""
  subscribeToUpdates: Boolean!
}

input ChangePasswordInput {
  """Current password"""
  currentPassword: String!

  """New password"""
  newPassword: String!
}

input CreateNeighborhoodInput {
  """SEO-friendly URL slug for the neighborhood"""
  slug: String!

  """Name of the neighborhood"""
  name: String!

  """Geographical location of the neighborhood"""
  location: LocationInput!

  """City ID this neighborhood belongs to"""
  city: String!

  """Main cover image of the neighborhood"""
  coverImage: String

  """Array of neighborhood image URLs"""
  images: [String!]
}

input UpdateNeighborhoodInput {
  """SEO-friendly URL slug for the neighborhood"""
  slug: String

  """Name of the neighborhood"""
  name: String

  """Geographical location of the neighborhood"""
  location: LocationInput

  """City ID this neighborhood belongs to"""
  city: String

  """Main cover image of the neighborhood"""
  coverImage: String

  """Array of neighborhood image URLs"""
  images: [String!]

  """Neighborhood ID"""
  id: ID!
}