import { ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Location, MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.input';

@ObjectType()
@Schema()
export class City extends MongooseSchema {
  /** Name of the city */
  @Prop({ required: true, index: true })
  name: string;

  /** Main image of the city */
  @Prop({ required: true })
  image: string;

  /** Cover image for the city */
  @Prop()
  coverImage?: string;

  /** Main heading for the city */
  @Prop({ required: true })
  heading: string;

  /** Sub heading for the city */
  @Prop()
  subHeading?: string;

  /** Geographical location of the city */
  @Prop({ required: true, type: Object })
  location: Location;
}

@ObjectType()
export class PaginatedCities extends createPaginatedType(City) {}

export const CitySchema = SchemaFactory.createForClass(City);

// Create 2dsphere index on location.center for geospatial queries
CitySchema.index({ 'location.center': '2dsphere' });
