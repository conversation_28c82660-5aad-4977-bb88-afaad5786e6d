import {
  Injectable,
  ConflictException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { PaginateModel, FilterQuery } from 'mongoose';
import { ClubCategory } from './entities/club-categories.entity';
import { CreateClubCategoryInput } from './dto/create-club-category.input';
import { UpdateClubCategoryInput } from './dto/update-club-category.input';
import { PaginationInput } from 'src/common/pagination.input';

@Injectable()
export class ClubCategoriesService {
  constructor(
    @InjectModel(ClubCategory.name)
    private clubCategoryModel: PaginateModel<ClubCategory>,
  ) {}

  async create(createClubCategoryInput: CreateClubCategoryInput) {
    // Check if category with same name already exists
    const existingCategory = await this.clubCategoryModel.findOne({
      name: { $regex: new RegExp(`^${createClubCategoryInput.name}$`, 'i') },
    });

    if (existingCategory) {
      throw new ConflictException(
        'Club category with this name already exists',
      );
    }

    return this.clubCategoryModel.create(createClubCategoryInput);
  }

  async findAll(
    filter: FilterQuery<ClubCategory> = {},
    paginationInput?: PaginationInput,
  ) {
    return this.clubCategoryModel.paginate(filter, paginationInput);
  }

  async findOne(id: string): Promise<ClubCategory> {
    const category = await this.clubCategoryModel.findById(id);

    if (!category) {
      throw new NotFoundException(`Club category with ID ${id} not found`);
    }

    return category;
  }

  async update(
    id: string,
    updateClubCategoryInput: UpdateClubCategoryInput,
  ): Promise<ClubCategory> {
    // Check if category exists
    const existingCategory = await this.clubCategoryModel.findById(id);
    if (!existingCategory) {
      throw new NotFoundException(`Club category with ID ${id} not found`);
    }

    // Check if name is being updated and if it conflicts with existing category
    if (
      updateClubCategoryInput.name &&
      updateClubCategoryInput.name !== existingCategory.name
    ) {
      const nameConflict = await this.clubCategoryModel.findOne({
        name: { $regex: new RegExp(`^${updateClubCategoryInput.name}$`, 'i') },
        _id: { $ne: id },
      });

      if (nameConflict) {
        throw new ConflictException(
          'Club category with this name already exists',
        );
      }
    }

    const updatedCategory = await this.clubCategoryModel.findByIdAndUpdate(
      id,
      updateClubCategoryInput,
      { new: true },
    );

    if (!updatedCategory) {
      throw new NotFoundException(`Club category with ID ${id} not found`);
    }

    return updatedCategory;
  }

  async remove(id: string): Promise<ClubCategory> {
    const category = await this.clubCategoryModel.findById(id);

    if (!category) {
      throw new NotFoundException(`Club category with ID ${id} not found`);
    }

    const deletedCategory = await this.clubCategoryModel.findByIdAndDelete(id);

    if (!deletedCategory) {
      throw new NotFoundException(`Club category with ID ${id} not found`);
    }

    return deletedCategory;
  }
}
