import { Injectable, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { PaginateModel, FilterQuery } from 'mongoose';
import { CreateAppLayoutInput } from './dto/create-app-layout.input';
import { UpdateAppLayoutInput } from './dto/update-app-layout.input';
import { AppLayout } from './entities/app-layout.entity';
import { PaginationInput } from 'src/common/pagination.input';

@Injectable()
export class AppLayoutService {
  constructor(
    @InjectModel(AppLayout.name)
    private appLayoutModel: PaginateModel<AppLayout>,
  ) {}

  async create(createAppLayoutInput: CreateAppLayoutInput) {
    // If this layout is being set as active, deactivate all other layouts
    if (createAppLayoutInput.active) {
      await this.appLayoutModel.updateMany({}, { active: false });
    }

    return this.appLayoutModel.create(createAppLayoutInput);
  }

  async findAll(
    filter: FilterQuery<AppLayout> = {},
    paginationInput?: PaginationInput,
  ) {
    return this.appLayoutModel.paginate(filter, paginationInput);
  }

  async findOne(id: string): Promise<AppLayout | null> {
    return this.appLayoutModel.findById(id);
  }

  async findActive(): Promise<AppLayout | null> {
    return this.appLayoutModel.findOne({ active: true });
  }

  async update(
    id: string,
    updateAppLayoutInput: UpdateAppLayoutInput,
  ): Promise<AppLayout | null> {
    // If this layout is being set as active, deactivate all other layouts
    if (updateAppLayoutInput.active) {
      await this.appLayoutModel.updateMany(
        { _id: { $ne: id } },
        { active: false },
      );
    }

    return this.appLayoutModel.findByIdAndUpdate(id, updateAppLayoutInput, {
      new: true,
    });
  }

  async remove(id: string): Promise<AppLayout | null> {
    return this.appLayoutModel.findByIdAndDelete(id);
  }

  async setActive(id: string): Promise<AppLayout | null> {
    // Deactivate all layouts first
    await this.appLayoutModel.updateMany({}, { active: false });

    // Activate the specified layout
    return this.appLayoutModel.findByIdAndUpdate(
      id,
      { active: true },
      { new: true },
    );
  }
}
