import {
  InputType,
  ObjectType,
  registerEnumType,
  ID,
  Field,
} from '@nestjs/graphql';
import { Prop, Schema, Virtual, SchemaFactory } from '@nestjs/mongoose';

@ObjectType()
@Schema({ timestamps: true })
export class MongooseSchema {
  /** MongoDB ObjectId */
  @Field(() => ID)
  _id: string;

  @Virtual({
    get(this: MongooseSchema) {
      return this._id.toString();
    },
  })
  /** String representation of the MongoDB ObjectId */
  @Field(() => ID)
  id: string;

  /** Document creation timestamp */

  createdAt: Date;

  /** Document last update timestamp */

  updatedAt: Date;
}

@ObjectType()
export class Location {
  /** Center coordinates as [longitude, latitude] */
  @Field(() => [Number])
  center: [number, number];
}

@ObjectType()
export class Address {
  /** Address */
  @Prop({ required: true })
  address: string;

  /** Location */
  @Prop({ required: true, type: Object })
  location: Location;

  /** Metro line */
  @Prop()
  metroLine?: string;

  /** Metro station */
  @Prop()
  metroStation?: string;
}

export enum DayOfWeek {
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
  SUNDAY = 'SUNDAY',
}
registerEnumType(DayOfWeek, { name: 'DayOfWeek' });

/**Inputs */
@InputType()
export class LocationInput {
  /** Center coordinates as [longitude, latitude] */
  @Field(() => [Number])
  center: [number, number];
}

@InputType()
export class AddressInput {
  /** Address */

  address: string;

  /** Location */
  location: LocationInput;
  /** Metro line */

  metroLine?: string;

  /** Metro station */
  metroStation?: string;
}

@InputType()
export class DayTimingInput {
  /** Day of the week */
  day: DayOfWeek;

  /** Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30). Supports formats that can be converted by time.utils: 900, 1730, etc. */
  @Field(() => [Number])
  timings: [number, number];
}

// Create schema for Address to enable geospatial indexing
export const AddressSchema = SchemaFactory.createForClass(Address);

// Create 2dsphere index on location.center for geospatial queries in Address
AddressSchema.index({ 'location.center': '2dsphere' });
