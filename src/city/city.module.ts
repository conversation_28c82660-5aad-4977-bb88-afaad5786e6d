import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CityService } from './city.service';
import { CityResolver } from './city.resolver';
import { City, CitySchema } from './entities/city.entity';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: City.name, schema: CitySchema }]),
  ],
  providers: [CityResolver, CityService],
  exports: [CityService],
})
export class CityModule {}
