import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { AppLayoutService } from './app-layout.service';
import { AppLayout, PaginatedAppLayouts } from './entities/app-layout.entity';
import { CreateAppLayoutInput } from './dto/create-app-layout.input';
import { UpdateAppLayoutInput } from './dto/update-app-layout.input';
import { AppLayoutsInput } from './dto/app-layouts.input';
import { PaginationInput } from 'src/common/pagination.input';
import * as _ from 'lodash';

@Resolver(() => AppLayout)
export class AppLayoutResolver {
  constructor(private readonly appLayoutService: AppLayoutService) {}

  @Mutation(() => AppLayout, { description: 'Create a new app layout' })
  createAppLayout(
    @Args('createAppLayoutInput') createAppLayoutInput: CreateAppLayoutInput,
  ): Promise<AppLayout> {
    return this.appLayoutService.create(createAppLayoutInput);
  }

  @Query(() => PaginatedAppLayouts, {
    name: 'appLayouts',
    description: 'Get all app layouts with pagination and filtering',
  })
  findAllAppLayouts(
    @Args('appLayoutsInput', { nullable: true })
    appLayoutsInput?: AppLayoutsInput,
    @Args('paginationInput', { type: () => PaginationInput, nullable: true })
    paginationInput?: PaginationInput,
  ) {
    const filter: any = {};

    if (appLayoutsInput?.name) {
      filter.name = { $regex: new RegExp(appLayoutsInput.name, 'i') };
    }

    if (appLayoutsInput?.active !== undefined) {
      filter.active = appLayoutsInput.active;
    }

    return this.appLayoutService.findAll(filter, paginationInput);
  }

  @Query(() => AppLayout, {
    name: 'appLayout',
    description: 'Get a single app layout by ID',
    nullable: true,
  })
  findAppLayoutById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<AppLayout | null> {
    return this.appLayoutService.findOne(id);
  }

  @Query(() => AppLayout, {
    name: 'activeAppLayout',
    description: 'Get the currently active app layout',
    nullable: true,
  })
  findActiveAppLayout(): Promise<AppLayout | null> {
    return this.appLayoutService.findActive();
  }

  @Mutation(() => AppLayout, {
    description: 'Update an existing app layout',
    nullable: true,
  })
  updateAppLayout(
    @Args('updateAppLayoutInput') updateAppLayoutInput: UpdateAppLayoutInput,
  ): Promise<AppLayout | null> {
    return this.appLayoutService.update(
      updateAppLayoutInput.id,
      updateAppLayoutInput,
    );
  }

  @Mutation(() => AppLayout, {
    description: 'Delete an app layout',
    nullable: true,
  })
  deleteAppLayout(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<AppLayout | null> {
    return this.appLayoutService.remove(id);
  }

  @Mutation(() => AppLayout, {
    description: 'Set an app layout as active',
    nullable: true,
  })
  setActiveAppLayout(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<AppLayout | null> {
    return this.appLayoutService.setActive(id);
  }
}
