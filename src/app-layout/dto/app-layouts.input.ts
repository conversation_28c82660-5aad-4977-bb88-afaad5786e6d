import { InputType, Field } from '@nestjs/graphql';
import { IsOptional, IsString, IsBoolean } from 'class-validator';

@InputType()
export class AppLayoutsInput {
  /** Filter by name (case-insensitive partial match) */
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  /** Filter by active status */
  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  active?: boolean;
}
