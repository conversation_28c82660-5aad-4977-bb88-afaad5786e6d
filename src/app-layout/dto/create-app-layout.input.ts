import { InputType, Field } from '@nestjs/graphql';
import {
  IsString,
  IsArray,
  IsBoolean,
  IsOptional,
  ValidateNested,
  IsEnum,
  IsNumber,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SectionType } from '../entities/app-layout.entity';
import { LocationInput } from 'src/common/common.entity';
import GraphQLJSON from 'graphql-type-json';

@InputType()
export class SectionInput {
  /** Title of the section */
  @Field()
  @IsString()
  title: string;

  /** Filters for the section */
  @Field(() => [GraphQLJSON])
  @IsArray()
  @IsOptional()
  filters?: any[];

  /** Type of the section */
  @Field(() => SectionType)
  @IsEnum(SectionType)
  type: SectionType;

  /** Availability by cities */
  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  availableInCities?: string[];

  /** Availability by neighborhoods */
  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  availableInNeighborhoods?: string[];

  /** Optional location */
  @Field(() => LocationInput, { nullable: true })
  @ValidateNested()
  @Type(() => LocationInput)
  @IsOptional()
  location?: LocationInput;

  /** If location is truthy then radius is required */
  @Field({ nullable: true })
  @IsNumber()
  @Min(1)
  @IsOptional()
  radius?: number;
}

@InputType()
export class CreateAppLayoutInput {
  /** Name of the app layout */
  @Field()
  @IsString()
  name: string;

  /** Sections in the app layout */
  @Field(() => [SectionInput])
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SectionInput)
  sections: SectionInput[];

  /** Whether the app layout is active (only one applayout can be active) */
  @Field({ nullable: true })
  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
