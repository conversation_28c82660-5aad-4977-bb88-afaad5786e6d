import {
  Injectable,
  ConflictException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { PaginateModel, FilterQuery } from 'mongoose';
import { BarCategory } from './entities/bar-categories.entity';
import { CreateBarCategoryInput } from './dto/create-bar-category.input';
import { UpdateBarCategoryInput } from './dto/update-bar-category.input';
import { PaginationInput } from 'src/common/pagination.input';

@Injectable()
export class BarCategoriesService {
  constructor(
    @InjectModel(BarCategory.name)
    private barCategoryModel: PaginateModel<BarCategory>,
  ) {}

  async create(createBarCategoryInput: CreateBarCategoryInput) {
    // Check if category with same name already exists
    const existingCategory = await this.barCategoryModel.findOne({
      name: { $regex: new RegExp(`^${createBarCategoryInput.name}$`, 'i') },
    });

    if (existingCategory) {
      throw new ConflictException(
        'Bar category with this name already exists',
      );
    }

    return this.barCategoryModel.create(createBarCategoryInput);
  }

  async findAll(
    filter: FilterQuery<BarCategory> = {},
    paginationInput?: PaginationInput,
  ) {
    return this.barCategoryModel.paginate(filter, paginationInput);
  }

  async findOne(id: string): Promise<BarCategory> {
    const category = await this.barCategoryModel.findById(id);

    if (!category) {
      throw new NotFoundException(`Bar category with ID ${id} not found`);
    }

    return category;
  }

  async update(
    id: string,
    updateBarCategoryInput: UpdateBarCategoryInput,
  ): Promise<BarCategory> {
    // Check if category exists
    const existingCategory = await this.barCategoryModel.findById(id);
    if (!existingCategory) {
      throw new NotFoundException(`Bar category with ID ${id} not found`);
    }

    // Check if name is being updated and if it conflicts with existing category
    if (
      updateBarCategoryInput.name &&
      updateBarCategoryInput.name !== existingCategory.name
    ) {
      const nameConflict = await this.barCategoryModel.findOne({
        name: { $regex: new RegExp(`^${updateBarCategoryInput.name}$`, 'i') },
        _id: { $ne: id },
      });

      if (nameConflict) {
        throw new ConflictException(
          'Bar category with this name already exists',
        );
      }
    }

    const updatedCategory = await this.barCategoryModel.findByIdAndUpdate(
      id,
      updateBarCategoryInput,
      { new: true },
    );

    if (!updatedCategory) {
      throw new NotFoundException(`Bar category with ID ${id} not found`);
    }

    return updatedCategory;
  }

  async remove(id: string): Promise<BarCategory> {
    const category = await this.barCategoryModel.findById(id);

    if (!category) {
      throw new NotFoundException(`Bar category with ID ${id} not found`);
    }

    const deletedCategory = await this.barCategoryModel.findByIdAndDelete(id);

    if (!deletedCategory) {
      throw new NotFoundException(`Bar category with ID ${id} not found`);
    }

    return deletedCategory;
  }
}
