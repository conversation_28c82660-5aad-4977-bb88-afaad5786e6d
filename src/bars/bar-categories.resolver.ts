import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { BarCategoriesService } from './bar-categories.service';
import {
  BarCategory,
  PaginatedBarCategories,
} from './entities/bar-categories.entity';
import { CreateBarCategoryInput } from './dto/create-bar-category.input';
import { UpdateBarCategoryInput } from './dto/update-bar-category.input';
import { BarCategoriesInput } from './dto/bar-categories.input';
import { PaginationInput } from 'src/common/pagination.input';
import * as _ from 'lodash';

@Resolver(() => BarCategory)
export class BarCategoriesResolver {
  constructor(private readonly barCategoriesService: BarCategoriesService) {}

  @Mutation(() => BarCategory, { description: 'Create a new bar category' })
  createBarCategory(
    @Args('createBarCategoryInput')
    createBarCategoryInput: CreateBarCategoryInput,
  ): Promise<BarCategory> {
    return this.barCategoriesService.create(createBarCategoryInput);
  }

  @Query(() => PaginatedBarCategories, {
    name: 'barCategories',
    description: 'Get all bar categories with pagination and filtering',
  })
  findAllBarCategories(
    @Args('barCategoriesInput', { nullable: true })
    barCategoriesInput?: BarCategoriesInput,
    @Args('paginationInput', { type: () => PaginationInput, nullable: true })
    paginationInput?: PaginationInput,
  ) {
    const filter = _.omitBy(
      _.omit(barCategoriesInput, 'pagination'),
      (value) => _.isUndefined(value),
    );

    // Add regex search for name if provided
    if (filter.name) {
      (filter.name as any) = { $regex: new RegExp(filter.name, 'i') };
    }

    return this.barCategoriesService.findAll(
      filter,
      paginationInput || barCategoriesInput?.pagination,
    );
  }

  @Query(() => BarCategory, {
    name: 'barCategory',
    description: 'Get a single bar category by ID',
  })
  findBarCategoryById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<BarCategory> {
    return this.barCategoriesService.findOne(id);
  }

  @Mutation(() => BarCategory, {
    description: 'Update an existing bar category',
  })
  updateBarCategory(
    @Args('updateBarCategoryInput')
    updateBarCategoryInput: UpdateBarCategoryInput,
  ): Promise<BarCategory> {
    return this.barCategoriesService.update(
      updateBarCategoryInput.id,
      updateBarCategoryInput,
    );
  }

  @Mutation(() => BarCategory, { description: 'Delete a bar category' })
  deleteBarCategory(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<BarCategory> {
    return this.barCategoriesService.remove(id);
  }
}
