import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, PaginateModel } from 'mongoose';
import { PaginationInput } from 'src/common/pagination.input';
import { CreateCityInput } from './dto/create-city.input';
import { UpdateCityInput } from './dto/update-city.input';
import { City } from './entities/city.entity';

@Injectable()
export class CityService {
  constructor(
    @InjectModel(City.name)
    private cityModel: PaginateModel<City>,
  ) {}

  async create(createCityInput: CreateCityInput) {
    // Check if city with same name already exists
    const existingCity = await this.cityModel.findOne({
      name: { $regex: new RegExp(`^${createCityInput.name}$`, 'i') },
    });

    if (existingCity) {
      throw new ConflictException('City with this name already exists');
    }

    return this.cityModel.create(createCityInput);
  }

  async findAll(
    filter: FilterQuery<City> = {},
    paginationInput?: PaginationInput,
  ) {
    return this.cityModel.paginate(filter, paginationInput);
  }

  async findOne(id: string): Promise<City> {
    const city = await this.cityModel.findById(id);

    if (!city) {
      throw new NotFoundException(`City with ID ${id} not found`);
    }

    return city;
  }

  async update(id: string, updateCityInput: UpdateCityInput): Promise<City> {
    // Check if city exists
    const existingCity = await this.cityModel.findById(id);
    if (!existingCity) {
      throw new NotFoundException(`City with ID ${id} not found`);
    }

    // Check if name is being updated and if it conflicts with another city
    if (updateCityInput.name && updateCityInput.name !== existingCity.name) {
      const nameConflict = await this.cityModel.findOne({
        name: { $regex: new RegExp(`^${updateCityInput.name}$`, 'i') },
        _id: { $ne: id },
      });

      if (nameConflict) {
        throw new ConflictException('City with this name already exists');
      }
    }

    const updatedCity = await this.cityModel.findByIdAndUpdate(
      id,
      updateCityInput,
      { new: true },
    );

    return updatedCity!;
  }

  async remove(id: string): Promise<City> {
    const city = await this.cityModel.findByIdAndDelete(id);

    if (!city) {
      throw new NotFoundException(`City with ID ${id} not found`);
    }

    return city;
  }
}
