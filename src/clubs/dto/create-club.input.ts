import { InputType, Field } from '@nestjs/graphql';
import {
  AddressInput,
  DayOfWeek,
  DayTimingInput,
  LocationInput,
} from 'src/common/common.entity';
import { ZodValidation } from 'src/common/zod-validator/decorators/zod-validation.decorator';
import { ContactInput } from 'src/users/dto/create-user.input';
import { z } from 'zod';
import { ClubStatus, MenuItemAvailability } from '../entities/club.entity';

const createClubInputSchema = z.object({
  name: z.string(),
  slug: z
    .string()
    .min(3, 'Slug must be at least 3 characters long')
    .max(100, 'Slug must not exceed 100 characters')
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      'Slug must be lowercase, contain only letters, numbers, and hyphens, and not start or end with a hyphen',
    ),
  description: z.string(),
  categories: z.array(z.string()),
  status: z.nativeEnum(ClubStatus),
  city: z.string(),
  neighborhood: z.string().optional(),
  location: z.object({
    center: z.array(z.number()).length(2),
    coords: z.array(z.array(z.number()).length(2)).optional(),
  }),
  logo: z.string().optional(),
  coverImage: z.string().optional(),
  images: z.array(z.string()),
  phone: z
    .object({
      countryCode: z.string(),
      phone: z.string(),
    })
    .optional(),
  address: z
    .object({
      address: z.string(),
      location: z.object({
        center: z.array(z.number()).length(2),
        coords: z.array(z.array(z.number()).length(2)).optional(),
      }),
      metroLine: z.string().optional(),
      metroStation: z.string().optional(),
    })
    .optional(),
  businessHours: z
    .object({
      schedule: z.array(
        z.object({
          day: z.nativeEnum(DayOfWeek),
          openingTime: z.string().optional(),
          closingTime: z.string().optional(),
        }),
      ),
    })
    .optional(),
  menu: z
    .object({
      currency: z.string(),
      sections: z.array(
        z.object({
          name: z.string(),
          items: z.array(
            z.object({
              name: z.string(),
              price: z.number().min(0),
              available: z.nativeEnum(MenuItemAvailability),
            }),
          ),
        }),
      ),
    })
    .optional(),
  featured: z.boolean(),
  rating: z.number().min(0).max(5),
});

@InputType()
export class BusinessHoursInput {
  /** Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format */
  schedule: DayTimingInput[];
}

@InputType()
export class MenuItemInput {
  /** Menu item name */
  name: string;

  /** Menu item price */
  price: number;

  /** Whether the item is available */
  available: MenuItemAvailability;
}

@InputType()
export class MenuSectionInput {
  /** Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats") */
  name: string;

  /** Array of menu items in this section */
  items: MenuItemInput[];
}

@InputType()
export class MenuInput {
  /** Menu currency (e.g., USD, EUR, INR) */
  currency: string;

  /** Array of menu sections */
  sections: MenuSectionInput[];
}

@ZodValidation(createClubInputSchema)
@InputType()
export class CreateClubInput {
  /** Club name */
  name: string;

  /** SEO-friendly URL slug for the club */
  @Field()
  slug: string;

  /** Club description */
  description: string;

  /** Club city IDs */
  city: string;

  /** Optional neighborhood ID */
  neighborhood?: string;

  /** Geographical location of the club */
  location: LocationInput;

  /** Club category IDs */
  categories: string[];

  /** Club status */
  status: ClubStatus;

  /** Club logo URL */
  logo?: string;

  /** Club cover image URL */
  coverImage?: string;

  /** Array of club image URLs */
  images: string[];

  /** Club contact information (phone) */
  contact?: ContactInput;

  /** Club address */
  address?: AddressInput;

  /** Opening hours */
  businessHours?: BusinessHoursInput;

  /** Club menu */
  menu?: MenuInput;

  /** Whether club is featured */
  featured: boolean;

  /** Club rating (0-5) */
  rating: number;
}
