import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { config, database, up } from 'migrate-mongo';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class MigrationService implements OnModuleInit {
  private readonly dbMigrationConfig: config.Config;

  constructor(private configService: ConfigService) {
    // Ensure migrations directory exists
    const migrationsDir = path.join(__dirname, '../../migrations');
    if (!fs.existsSync(migrationsDir)) {
      fs.mkdirSync(migrationsDir, { recursive: true });
      console.log('Created migrations directory:', migrationsDir);
    }

    this.dbMigrationConfig = {
      mongodb: {
        databaseName: this.configService.get('DATABASE_NAME')!,
        url: this.configService.get('DATABASE_URI')!,
      },
      migrationsDir: migrationsDir,
      changelogCollectionName: 'migrations',
      migrationFileExtension: '.js',
    };
  }
  async onModuleInit() {
    console.log('Checking for migrations');

    config.set(this.dbMigrationConfig);
    const { client, db } = await database.connect();
    await up(db, client);
  }
}
